<?php
// Shipments index view
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <a href="<?= App\Core\View::url('/admin/shipments/create') ?>" class="btn btn-primary">
        <i class="fas fa-plus"></i> New Shipment
    </a>
</div>

<?php if (isset($flash_success)): ?>
    <div class="alert alert-success">
        <?= App\Core\View::e($flash_success) ?>
    </div>
<?php endif; ?>

<?php if (isset($flash_error)): ?>
    <div class="alert alert-error">
        <?= App\Core\View::e($flash_error) ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">All Shipments</div>
    <div class="card-body">
        <div class="table-container">
            <?php if (empty($shipments)): ?>
                <p class="text-muted">No shipments found.</p>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Tracking #</th>
                            <th>Origin</th>
                            <th>Destination</th>
                            <th>Sender</th>
                            <th>Recipient</th>
                            <th>Status</th>
                            <th class="hide-on-smaller-screens">Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($shipments as $shipment): ?>
                            <tr>
                                <td><?= App\Core\View::e($shipment['id']) ?></td>
                                <td><?= App\Core\View::e($shipment['tracking_number']) ?></td>
                                <td><?= App\Core\View::e($shipment['origin']) ?></td>
                                <td><?= App\Core\View::e($shipment['destination']) ?></td>
                                <td><?= App\Core\View::e($shipment['shipper_name'] ?? 'N/A') ?></td>
                                <td><?= App\Core\View::e($shipment['receiver_name'] ?? 'N/A') ?></td>
                                <td>
                                    <?php
                                    $statusClass = 'bg-primary';
                                    switch ($shipment['status']) {
                                        case 'pending':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'in_transit':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'out_for_delivery':
                                            $statusClass = 'bg-primary';
                                            break;
                                        case 'delivered':
                                            $statusClass = 'bg-success';
                                            break;
                                        case 'delayed':
                                            $statusClass = 'bg-danger';
                                            break;
                                        case 'cancelled':
                                            $statusClass = 'bg-secondary';
                                            break;
                                        case 'picked_up':
                                            $statusClass = 'bg-info';
                                            break;
                                        case 'on_hold':
                                            $statusClass = 'bg-warning text-dark';
                                            break;
                                        case 'returned':
                                            $statusClass = 'bg-danger';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?= $statusClass ?>">
                                        <?= App\Core\View::e(ucfirst(str_replace('_', ' ', $shipment['status']))) ?>
                                    </span>
                                </td>
                                <td class="hide-on-smaller-screens"><?= App\Core\View::e(date('Y-m-d', strtotime($shipment['created_at']))) ?></td>
                                <td>
                                    <div class="btn-group action-buttons" role="group" aria-label="Shipment Actions">
                                        <a href="<?= App\Core\View::url('/admin/shipments/view/' . $shipment['id']) ?>" class="btn btn-sm btn-primary" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-success" title="Edit" onclick="openEditModal(<?= $shipment['id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" title="Delete" onclick="confirmDelete(<?= $shipment['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    <form id="delete-form-<?= $shipment['id'] ?>" action="<?= App\Core\View::url('/admin/shipments/delete/' . $shipment['id']) ?>" method="POST" style="display: none;">
                                        <?= App\Core\View::csrfField() ?>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include __DIR__ . '/partials/_edit_modal.php'; ?>

<script>
    function openEditModal(shipmentId) {
        // Open the modal
        const modal = new bootstrap.Modal(document.getElementById('editShipmentModal'));
        modal.show();

        // Load the shipment data
        window.loadShipmentForEdit(shipmentId);
    }

    function confirmDelete(shipmentId) {
        if (confirm('Are you sure you want to delete this shipment? This action cannot be undone.')) {
            document.getElementById('delete-form-' + shipmentId).submit();
        }
    }
</script>
