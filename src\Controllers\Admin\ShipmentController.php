<?php

namespace App\Controllers\Admin;

use App\Core\Controller;
use App\Core\Session;
use App\Core\View;
use App\Models\Shipment;
use App\Models\ShipmentHistory;
use App\Models\ShipmentPackage;
use App\Models\Address;
use PDO;

class ShipmentController extends Controller
{
    // Placeholder for auth check - apply middleware later
    public function __construct()
    {
        parent::__construct(); // Call parent constructor for request sanitization
        // --- Auth Check Placeholder ---
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access this section.');
             // Cannot redirect from constructor, middleware should handle this.
             // For now, methods will need to check individually or we assume middleware runs.
             // Let's add checks in each method for now.
        }
        // --- End Auth Check ---
    }

    private function checkAuth(): bool
    {
        if (!Session::has('admin_user_id')) {
             Session::flash('error', 'Please log in to access this section.');
             $this->redirect('/admin/login');
             return false; // Indicate redirection happened
        }
        return true;
    }

    /**
     * List all shipments.
     */
    public function index()
    {
        if (!$this->checkAuth()) return; // Stop execution if redirected

        try {
            // Fetch shipments from database
            $shipments = Shipment::all();

            return $this->view('admin.shipments.index', [
                'pageTitle' => 'Manage Shipments',
                'shipments' => $shipments
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment List Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading shipments: ' . $e->getMessage());
            return $this->redirect('/admin/dashboard');
        }
    }

    /**
     * List all shipments (alias for index).
     */
    public function listShipments()
    {
        return $this->index();
    }

    /**
     * Show form to create a new shipment.
     */
    public function create()
    {
        if (!$this->checkAuth()) return;

        try {
            // Load data for dropdowns
            $clients = [];
            $employees = [];
            $statusOptions = [];
            $shipmentTypes = [];
            $shipmentModes = [];
            $carriers = [];
            $paymentModes = [];
            $locations = []; // For origin/destination
            $clientAddresses = []; // Will be loaded via AJAX

            if (class_exists('\App\Models\Client')) {
                $clients = \App\Models\Client::all();
            }

            if (class_exists('\App\Models\Employee')) {
                // Assuming an Employee model with a method like allActive()
                // $employees = \App\Models\Employee::allActive();
                // If not, use all() or adjust as needed
                // $employees = \App\Models\Employee::all();
            }

            if (class_exists('\App\Models\StatusOption')) {
                 // Assuming a StatusOption model
                // $statusOptions = \App\Models\StatusOption::all();
            }

            // Get settings for dropdowns from Settings model or use defaults
            if (class_exists('\App\Models\Setting')) {
                $shipmentTypes = \App\Models\Setting::getAsArray('shipment_types', []);
                $shipmentModes = \App\Models\Setting::getAsArray('shipment_modes', []);
                $carriers = \App\Models\Setting::getAsArray('carriers', []);
                $paymentModes = \App\Models\Setting::getAsArray('payment_modes', []);
                $locations = \App\Models\Setting::getAsArray('locations', []); // Assuming locations are stored as a setting
            }

            // --- Default values if settings are empty or model doesn't exist ---
            if (empty($shipmentTypes)) {
                $shipmentTypes = ['Document', 'Parcel', 'Cargo', 'Express', 'Standard'];
            }
            if (empty($shipmentModes)) {
                $shipmentModes = ['Air', 'Sea', 'Land', 'Rail', 'Multimodal'];
            }
            if (empty($carriers)) {
                $carriers = ['DHL', 'FedEx', 'UPS', 'USPS', 'TNT', 'Other'];
            }
            if (empty($paymentModes)) {
                $paymentModes = ['Cash', 'Credit Card', 'Bank Transfer', 'PayPal', 'Account', 'COD'];
            }
            if (empty($locations)) {
                // Add a default list of countries/locations if needed
                $locations = ['USA', 'Canada', 'Mexico', 'United Kingdom', 'Germany', 'France'];
            }
            // Add default status options if StatusOption model/data is unavailable
            if (empty($statusOptions)) {
                 $statusOptions = [
                    'pending' => 'Pending',
                    'picked_up' => 'Picked Up',
                    'on_hold' => 'On Hold',
                    'in_transit' => 'In Transit',
                    'out_for_delivery' => 'Out for Delivery',
                    'delivered' => 'Delivered',
                    'cancelled' => 'Cancelled',
                    'returned' => 'Returned'
                 ];
            }
            // --- End Defaults ---

            // Get package types from settings
            $packageTypes = [];
            if (class_exists('\App\Models\Setting')) {
                $packageTypes = \App\Models\Setting::getAsArray('package_types', []);
            }

            // Generate a tracking number
            $generatedTrackingNumber = Shipment::generateTrackingNumber();

            return $this->view('admin.shipments.create', [
                'pageTitle' => 'Create New Shipment',
                'formAction' => '/admin/shipments/create', // Use the create route
                'shipment' => null,
                'clients' => $clients,
                'employees' => $employees,
                'statusOptions' => $statusOptions,
                'shipmentTypes' => $shipmentTypes,
                'shipmentModes' => $shipmentModes,
                'carriers' => $carriers,
                'paymentModes' => $paymentModes,
                'locations' => $locations,
                'clientAddresses' => $clientAddresses, // Initially empty
                'generatedTrackingNumber' => $generatedTrackingNumber,
                'packageTypes' => $packageTypes,
                'isEdit' => false
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            error_log('Shipment Create Form Error: ' . $e->getMessage());
            Session::flash('error', 'Error loading create form: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Store a newly created shipment.
     */
    public function store()
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Validate input
            $validator = $this->validate($this->input(), [
                'tracking_number' => 'required|min:5',
                'origin' => 'required',
                'destination' => 'required',
                'shipper_name' => 'required',
                'shipper_address' => 'required',
                'shipper_phone' => 'required',
                'receiver_name' => 'required',
                'receiver_address' => 'required',
                'receiver_phone' => 'required',
                'status' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Create the shipment
            $shipmentId = Shipment::create($this->input());

            if (!$shipmentId) {
                Session::flash('error', 'Failed to create shipment.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Create initial history entry
            ShipmentHistory::create([
                'shipment_id' => $shipmentId,
                'status' => $this->input('status'),
                'location' => $this->input('origin'),
                'description' => 'Shipment created'
            ]);

            // Process and save packages if present
            $packages = $this->input('packages', []);
            if (is_array($packages) && !empty($packages)) {
                foreach ($packages as $package) {
                    // Skip empty package entries
                    if (empty($package['piece_type']) && empty($package['weight']) && empty($package['length']) &&
                        empty($package['width']) && empty($package['height'])) {
                        continue;
                    }

                    // Prepare package data
                    $packageData = [
                        'shipment_id' => $shipmentId,
                        'piece_type' => $package['piece_type'] ?? '',
                        'description' => $package['description'] ?? '',
                        'quantity' => intval($package['quantity'] ?? 1),
                        'length' => floatval($package['length'] ?? 0),
                        'width' => floatval($package['width'] ?? 0),
                        'height' => floatval($package['height'] ?? 0),
                        'weight' => floatval($package['weight'] ?? 0),
                        'volume' => floatval($package['volume'] ?? 0),
                        'volumetric_weight' => floatval($package['volumetric_weight'] ?? 0)
                    ];

                    // Create the package
                    ShipmentPackage::create($packageData);
                }

                // Update total weight in the shipment if it wasn't provided
                if (empty($this->input('weight'))) {
                    $totalWeight = ShipmentPackage::calculateTotalWeight($shipmentId);
                    Shipment::update($shipmentId, ['weight' => $totalWeight]);
                }
            }

            // Redirect to the shipment list with success message
            Session::flash('success', 'Shipment created successfully.');
            return $this->redirect('/admin/shipments');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Store Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error creating shipment: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Show form to edit an existing shipment.
     * @param string|int $id
     */
    public function edit($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Use the new method to get complete shipment data
            $shipment = Shipment::findWithRelations($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found or database error occurred');
                error_log("Shipment not found for ID: $id"); // Log the missing ID
                return $this->redirect('/admin/shipments');
            }

            // Debug the shipment data
            error_log('Editing shipment: ' . json_encode($shipment));

            // Get packages for this shipment
            $shipmentPackages = ShipmentPackage::findByShipmentId($id);
            error_log('Shipment packages: ' . json_encode($shipmentPackages));

            // Add packages to shipment data for form
            $shipment['packages'] = $shipmentPackages;

            // Load data for dropdowns
            $clients = [];
            $employees = [];
            $statusOptions = [];
            $shipmentTypes = [];
            $shipmentModes = [];
            $carriers = [];
            $paymentModes = [];
            $locations = [];
            $clientAddresses = [];

            if (class_exists('\App\Models\Client')) {
                $clients = \App\Models\Client::all();
            }
            if (class_exists('\App\Models\Employee')) {
                // $employees = \App\Models\Employee::allActive();
            }
             if (class_exists('\App\Models\StatusOption')) {
                // $statusOptions = \App\Models\StatusOption::all();
            }
            if (class_exists('\App\Models\Setting')) {
                $shipmentTypes = \App\Models\Setting::getAsArray('shipment_types', []);
                $shipmentModes = \App\Models\Setting::getAsArray('shipment_modes', []);
                $carriers = \App\Models\Setting::getAsArray('carriers', []);
                $paymentModes = \App\Models\Setting::getAsArray('payment_modes', []);
                $locations = \App\Models\Setting::getAsArray('locations', []);
            }

             // --- Default values if settings are empty or model doesn't exist ---
            if (empty($shipmentTypes)) {
                $shipmentTypes = ['Document', 'Parcel', 'Cargo', 'Express', 'Standard'];
            }
            if (empty($shipmentModes)) {
                $shipmentModes = ['Air', 'Sea', 'Land', 'Rail', 'Multimodal'];
            }
            if (empty($carriers)) {
                $carriers = ['DHL', 'FedEx', 'UPS', 'USPS', 'TNT', 'Other'];
            }
            if (empty($paymentModes)) {
                $paymentModes = ['Cash', 'Credit Card', 'Bank Transfer', 'PayPal', 'Account', 'COD'];
            }
            if (empty($locations)) {
                $locations = ['USA', 'Canada', 'Mexico', 'United Kingdom', 'Germany', 'France'];
            }
            if (empty($statusOptions)) {
                 $statusOptions = [
                    'pending' => 'Pending',
                    'picked_up' => 'Picked Up',
                    'on_hold' => 'On Hold',
                    'in_transit' => 'In Transit',
                    'out_for_delivery' => 'Out for Delivery',
                    'delivered' => 'Delivered',
                    'cancelled' => 'Cancelled',
                    'returned' => 'Returned'
                 ];
            }
            // --- End Defaults ---

            // Get client addresses for the selected client if Address model exists
            if (!empty($shipment['client_id']) && class_exists('\App\Models\Address')) {
                // Assuming an Address model with findByClientId method
                // $clientAddresses = \App\Models\Address::findByClientId($shipment['client_id']);
            }

            // Get packages for this shipment
            $shipmentPackages = ShipmentPackage::findByShipmentId($id);

            // Get package types from settings
            $packageTypes = [];
            if (class_exists('\App\Models\Setting')) {
                $packageTypes = \App\Models\Setting::getAsArray('package_types', []);
            }

            return $this->view('admin.shipments.edit', [
                'pageTitle' => 'Edit Shipment #' . $id,
                'formAction' => '/admin/shipments/edit/' . $id, // Use the edit route
                'shipment' => $shipment,
                'clients' => $clients,
                'employees' => $employees,
                'statusOptions' => $statusOptions,
                'shipmentTypes' => $shipmentTypes,
                'shipmentModes' => $shipmentModes,
                'carriers' => $carriers,
                'paymentModes' => $paymentModes,
                'locations' => $locations,
                'clientAddresses' => $clientAddresses,
                'shipmentPackages' => $shipmentPackages,
                'packageTypes' => $packageTypes,
                'isEdit' => true
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            error_log('Shipment Edit Form Error: ' . $e->getMessage());
            Session::flash('error', 'Error loading edit form: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Show the dedicated page for editing an existing shipment.
     * @param string|int $id
     */
    public function editPage($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Use the method that gets complete shipment data
            $shipment = Shipment::findWithRelations($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found or database error occurred');
                error_log("Shipment not found for ID: $id in editPage");
                return $this->redirect('/admin/shipments');
            }

            // Debug the shipment data
            error_log('Loading edit page for shipment: ' . json_encode($shipment));

            // Get packages for this shipment
            $shipmentPackages = ShipmentPackage::findByShipmentId($id);
            error_log('Shipment packages for edit page: ' . json_encode($shipmentPackages));

            // Add packages to shipment data for form
            $shipment['packages'] = $shipmentPackages;

            // Load data for dropdowns (similar to create/edit methods)
            $clients = class_exists('\App\Models\Client') ? \App\Models\Client::all() : [];
            $employees = []; // Load employees if needed
            $statusOptions = class_exists('\App\Models\StatusOption') ? \App\Models\StatusOption::getActive() : [];
            $shipmentTypes = class_exists('\App\Models\Setting') ? \App\Models\Setting::getAsArray('shipment_types', []) : [];
            $shipmentModes = class_exists('\App\Models\Setting') ? \App\Models\Setting::getAsArray('shipment_modes', []) : [];
            $carriers = class_exists('\App\Models\Setting') ? \App\Models\Setting::getAsArray('carriers', []) : [];
            $paymentModes = class_exists('\App\Models\Setting') ? \App\Models\Setting::getAsArray('payment_modes', []) : [];
            $locations = class_exists('\App\Models\Setting') ? \App\Models\Setting::getAsArray('locations', []) : [];
            $clientAddresses = []; // Load via AJAX or if client_id is present
            if (!empty($shipment['client_id']) && class_exists('\App\Models\Address')) {
                $clientAddresses = \App\Models\Address::findByClientId($shipment['client_id']);
            }

            // Render the NEW edit page view
            return $this->view('admin.shipments.edit_page', [
                'pageTitle' => 'Edit Shipment #' . $id,
                'formAction' => '/admin/shipments/update/' . $id, // Submit to the existing update method
                'shipment' => $shipment,
                'clients' => $clients,
                'employees' => $employees,
                'statusOptions' => $statusOptions,
                'shipmentTypes' => $shipmentTypes,
                'shipmentModes' => $shipmentModes,
                'carriers' => $carriers,
                'paymentModes' => $paymentModes,
                'locations' => $locations,
                'clientAddresses' => $clientAddresses,
                'shipmentPackages' => $shipmentPackages, // Pass packages separately too for clarity
                'isEdit' => true // Keep this flag for partials
            ], 'admin.layouts.main');

        } catch (\Exception $e) {
            error_log('Shipment Edit Page Error: ' . $e->getMessage());
            Session::flash('error', 'Error loading edit page: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Update an existing shipment.
     * @param string|int $id
     */
    public function update($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found.');
                return $this->redirect('/admin/shipments');
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'tracking_number' => 'required|min:5',
                'origin' => 'required',
                'destination' => 'required',
                'shipper_name' => 'required',
                'shipper_address' => 'required',
                'shipper_phone' => 'required',
                'receiver_name' => 'required',
                'receiver_address' => 'required',
                'receiver_phone' => 'required',
                'status' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Check if status has changed
            $oldStatus = $shipment['status'];
            $newStatus = $this->input('status');

            // Prepare input data
            $inputData = $this->input();

            // Update the shipment
            $success = Shipment::update($id, $inputData);

            if (!$success) {
                Session::flash('error', 'Failed to update shipment.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Add history entry if status changed
            if ($oldStatus !== $newStatus) {
                ShipmentHistory::create([
                    'shipment_id' => $id,
                    'status' => $newStatus,
                    'location' => $this->input('location', $this->input('destination')),
                    'description' => 'Status changed from ' . $oldStatus . ' to ' . $newStatus
                ]);
            }

            // Process packages - delete existing ones and add new ones
            $packages = $this->input('packages', []);
            if (is_array($packages)) {
                // Delete all existing packages for this shipment
                ShipmentPackage::deleteByShipmentId($id);

                // Create new package entries
                foreach ($packages as $package) {
                    // Skip empty package entries
                    if (empty($package['piece_type']) && empty($package['weight']) && empty($package['length']) &&
                        empty($package['width']) && empty($package['height'])) {
                        continue;
                    }

                    // Prepare package data
                    $packageData = [
                        'shipment_id' => $id,
                        'piece_type' => $package['piece_type'] ?? '',
                        'description' => $package['description'] ?? '',
                        'quantity' => intval($package['quantity'] ?? 1),
                        'length' => floatval($package['length'] ?? 0),
                        'width' => floatval($package['width'] ?? 0),
                        'height' => floatval($package['height'] ?? 0),
                        'weight' => floatval($package['weight'] ?? 0),
                        'volume' => floatval($package['volume'] ?? 0),
                        'volumetric_weight' => floatval($package['volumetric_weight'] ?? 0)
                    ];

                    // Create the package
                    ShipmentPackage::create($packageData);
                }

                // Update total weight in the shipment if it wasn't provided
                if (empty($this->input('weight'))) {
                    $totalWeight = ShipmentPackage::calculateTotalWeight($id);
                    Shipment::update($id, ['weight' => $totalWeight]);
                }
            }

            // Check if this is an AJAX request
            $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

            if ($isAjax) {
                // Return JSON response for AJAX requests
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'message' => 'Shipment updated successfully.'
                ]);
                exit;
            } else {
                // Redirect to the shipment list with success message for regular requests
                Session::flash('success', 'Shipment updated successfully.');
                return $this->redirect('/admin/shipments');
            }
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Update Error: ' . $e->getMessage());

            // Check if this is an AJAX request
            $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

            if ($isAjax) {
                // Return JSON error response for AJAX requests
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'message' => 'Error updating shipment: ' . $e->getMessage()
                ]);
                exit;
            } else {
                // Show error message for regular requests
                Session::flash('error', 'Error updating shipment: ' . $e->getMessage());
                return $this->redirectBackWithError([], $this->input());
            }
        }
    }

    /**
     * View a shipment's details.
     * @param string|int $id
     */
    public function viewShipment($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found.');
                return $this->redirect('/admin/shipments');
            }

            // Get shipment history
            $history = ShipmentHistory::findByShipmentId($id);

            // Get packages for this shipment
            $shipmentPackages = ShipmentPackage::findByShipmentId($id);

            // Get status options for the dropdown
            $statusOptions = [];
            if (class_exists('\App\Models\StatusOption')) {
                $statusOptions = \App\Models\StatusOption::getActive();
            }

            // Render the view
            return $this->view('admin.shipments.view', [
                'pageTitle' => 'View Shipment #' . $id,
                'shipment' => $shipment,
                'history' => $history,
                'shipmentPackages' => $shipmentPackages,
                'statusOptions' => $statusOptions
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment View Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error viewing shipment: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Delete a shipment.
     * @param string|int $id
     */
    public function delete($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found.');
                return $this->redirect('/admin/shipments');
            }

            // Delete shipment history first
            ShipmentHistory::deleteByShipmentId($id);

            // Delete the shipment
            $success = Shipment::delete($id);

            if (!$success) {
                Session::flash('error', 'Failed to delete shipment.');
                return $this->redirect('/admin/shipments');
            }

            // Redirect to the shipment list with success message
            Session::flash('success', 'Shipment deleted successfully.');
            return $this->redirect('/admin/shipments');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Delete Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error deleting shipment: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Get addresses for a client (AJAX endpoint)
     * @param string|int $clientId
     */
    public function getClientAddresses($clientId)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get addresses for the client
            $addresses = Address::findByClientId($clientId);

            // Return JSON response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'addresses' => $addresses
            ]);
            exit;
        } catch (\Exception $e) {
            // Return error response
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'Error loading addresses: ' . $e->getMessage()
            ]);
            exit;
        }
    }

    /**
     * Show the status update form for a shipment
     * @param string|int $id
     */
    public function updateStatusForm($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found.');
                return $this->redirect('/admin/shipments');
            }

            // Get shipment history
            $history = ShipmentHistory::findByShipmentId($id);

            // Get status options for the dropdown
            $statusOptions = [];
            if (class_exists('\App\Models\StatusOption')) {
                $statusOptions = \App\Models\StatusOption::getActive();
            }

            // Add default status options if StatusOption model/data is unavailable
            if (empty($statusOptions)) {
                $statusOptions = [
                    'pending' => 'Pending',
                    'picked_up' => 'Picked Up',
                    'on_hold' => 'On Hold',
                    'in_transit' => 'In Transit',
                    'out_for_delivery' => 'Out for Delivery',
                    'delivered' => 'Delivered',
                    'cancelled' => 'Cancelled',
                    'returned' => 'Returned'
                ];
            }

            return $this->view('admin.shipments.update_status', [
                'pageTitle' => 'Update Shipment Status #' . $id,
                'shipment' => $shipment,
                'history' => $history,
                'statusOptions' => $statusOptions
            ], 'admin.layouts.main');
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Status Update Form Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error loading status update form: ' . $e->getMessage());
            return $this->redirect('/admin/shipments');
        }
    }

    /**
     * Add a history entry to a shipment and update its status
     * @param string|int $id
     */
    public function updateStatus($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                Session::flash('error', 'Shipment not found.');
                return $this->redirect('/admin/shipments');
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'status' => 'required',
                'location' => 'required',
                'status_date' => 'required',
                'status_time' => 'required'
            ]);

            if ($validator->fails()) {
                return $this->redirectBackWithError($validator->errors(), $this->input());
            }

            // Prepare date_time from the separate date and time inputs
            $dateTime = $this->input('status_date') . ' ' . $this->input('status_time') . ':00';

            // Create history entry
            $historyId = ShipmentHistory::create([
                'shipment_id' => $id,
                'status' => $this->input('status'),
                'location' => $this->input('location'),
                'message' => $this->input('description') ?: 'Status updated to ' . ucfirst(str_replace('_', ' ', $this->input('status'))),
                'date_time' => $dateTime
            ]);

            if (!$historyId) {
                Session::flash('error', 'Failed to add history entry.');
                return $this->redirectBackWithError([], $this->input());
            }

            // Update shipment status
            Shipment::update($id, [
                'status' => $this->input('status'),
                'current_location' => $this->input('location')
            ]);

            // Send notification to receiver if requested
            if ($this->input('notify_receiver') && !empty($shipment['receiver_email'])) {
                $this->sendStatusUpdateNotification($shipment, $this->input('status'), $this->input('location'), $dateTime);
            }

            // Redirect to the shipment view page with success message
            Session::flash('success', 'Shipment status updated successfully.');
            return $this->redirect('/admin/shipments/view/' . $id);
        } catch (\Exception $e) {
            // Log the error
            error_log('Shipment Add History Error: ' . $e->getMessage());

            // Show error message
            Session::flash('error', 'Error updating shipment status: ' . $e->getMessage());
            return $this->redirectBackWithError([], $this->input());
        }
    }

    /**
     * Send notification email to receiver about status update
     *
     * @param array $shipment Shipment data
     * @param string $status New status
     * @param string $location Current location
     * @param string $dateTime Date and time of update
     * @return bool Success status
     */
    private function sendStatusUpdateNotification(array $shipment, string $status, string $location, string $dateTime): bool
    {
        try {
            // Check if we have receiver email
            if (empty($shipment['receiver_email'])) {
                return false;
            }

            // Format status for display
            $formattedStatus = ucfirst(str_replace('_', ' ', $status));

            // Format date for display
            $formattedDate = date('F j, Y g:i A', strtotime($dateTime));

            // Prepare email subject
            $subject = 'Shipment Update: ' . $shipment['tracking_number'] . ' - ' . $formattedStatus;

            // Prepare email body
            $body = "<html><body>";
            $body .= "<h2>Shipment Status Update</h2>";
            $body .= "<p>Hello " . htmlspecialchars($shipment['receiver_name']) . ",</p>";
            $body .= "<p>Your shipment with tracking number <strong>" . htmlspecialchars($shipment['tracking_number']) . "</strong> has been updated.</p>";
            $body .= "<p><strong>New Status:</strong> " . htmlspecialchars($formattedStatus) . "</p>";
            $body .= "<p><strong>Location:</strong> " . htmlspecialchars($location) . "</p>";
            $body .= "<p><strong>Date & Time:</strong> " . htmlspecialchars($formattedDate) . "</p>";

            // Add tracking link
            $trackingUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/track?number=' . urlencode($shipment['tracking_number']);
            $body .= "<p>You can track your shipment at any time by visiting: <a href='" . $trackingUrl . "'>" . $trackingUrl . "</a></p>";

            $body .= "<p>Thank you for choosing our services.</p>";
            $body .= "<p>Best regards,<br>The Shipping Team</p>";
            $body .= "</body></html>";

            // Set headers for HTML email
            $headers = "MIME-Version: 1.0\r\n";
            $headers .= "Content-type: text/html; charset=UTF-8\r\n";
            $headers .= "From: no-reply@" . $_SERVER['HTTP_HOST'] . "\r\n";

            // Send email
            $mailSent = mail($shipment['receiver_email'], $subject, $body, $headers);

            // Log email sending attempt
            if ($mailSent) {
                error_log('Status update notification sent to ' . $shipment['receiver_email'] . ' for shipment ' . $shipment['tracking_number']);
            } else {
                error_log('Failed to send status update notification to ' . $shipment['receiver_email'] . ' for shipment ' . $shipment['tracking_number']);
            }

            return $mailSent;
        } catch (\Exception $e) {
            error_log('Error sending notification: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the edit form for a shipment via AJAX
     * @param string|int $id
     */
    public function getEditForm($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Get the shipment data
            $shipment = Shipment::findWithRelations($id);

            if (!$shipment) {
                http_response_code(404);
                echo "<div class='alert alert-danger'>Shipment not found</div>";
                exit;
            }

            // Get packages for this shipment
            $shipmentPackages = ShipmentPackage::findByShipmentId($id);
            $shipment['packages'] = $shipmentPackages;

            // Load data for dropdowns
            $clients = [];
            $employees = [];
            $statusOptions = [];
            $shipmentTypes = [];
            $shipmentModes = [];
            $carriers = [];
            $paymentModes = [];
            $locations = [];
            $clientAddresses = [];

            // Load clients
            if (class_exists('\App\Models\Client')) {
                $clients = \App\Models\Client::all();
            }

            // Load status options
            if (class_exists('\App\Models\StatusOption')) {
                $statusOptions = \App\Models\StatusOption::getActive();
            } else {
                $statusOptions = [
                    'pending' => 'Pending',
                    'picked_up' => 'Picked Up',
                    'on_hold' => 'On Hold',
                    'in_transit' => 'In Transit',
                    'out_for_delivery' => 'Out for Delivery',
                    'delivered' => 'Delivered',
                    'cancelled' => 'Cancelled',
                    'returned' => 'Returned'
                ];
            }

            // Load settings
            if (class_exists('\App\Models\Setting')) {
                $shipmentTypes = \App\Models\Setting::getAsArray('shipment_types', []);
                $shipmentModes = \App\Models\Setting::getAsArray('shipment_modes', []);
                $carriers = \App\Models\Setting::getAsArray('carriers', []);
                $paymentModes = \App\Models\Setting::getAsArray('payment_modes', []);
                $locations = \App\Models\Setting::getAsArray('locations', []);
            }

            // Default values if settings are empty
            if (empty($shipmentTypes)) {
                $shipmentTypes = ['Document', 'Parcel', 'Cargo', 'Express', 'Standard'];
            }
            if (empty($shipmentModes)) {
                $shipmentModes = ['Air', 'Sea', 'Land', 'Rail', 'Multimodal'];
            }
            if (empty($carriers)) {
                $carriers = ['DHL', 'FedEx', 'UPS', 'USPS', 'TNT', 'Other'];
            }
            if (empty($paymentModes)) {
                $paymentModes = ['Cash', 'Credit Card', 'Bank Transfer', 'PayPal', 'Account', 'COD'];
            }
            if (empty($locations)) {
                $locations = ['USA', 'Canada', 'Mexico', 'United Kingdom', 'Germany', 'France'];
            }

            // Get client addresses if needed
            if (!empty($shipment['client_id']) && class_exists('\App\Models\Address')) {
                $clientAddresses = \App\Models\Address::findByClientId($shipment['client_id']);
            }

            // Render the edit form
            ob_start();
            include __DIR__ . '/../../Views/admin/shipments/partials/_edit_form.php';
            $formHtml = ob_get_clean();

            echo $formHtml;
            exit;

        } catch (\Exception $e) {
            error_log('Error loading edit form: ' . $e->getMessage());
            http_response_code(500);
            echo "<div class='alert alert-danger'>Error loading shipment data: " . $e->getMessage() . "</div>";
            exit;
        }
    }

    /**
     * Request a document for a shipment
     * @param string|int $id
     */
    public function requestDocument($id)
    {
        if (!$this->checkAuth()) return;

        try {
            // Verify CSRF token
            $this->verifyCsrf();

            // Get the shipment
            $shipment = Shipment::find($id);

            if (!$shipment) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Shipment not found']);
                exit;
            }

            // Validate input
            $validator = $this->validate($this->input(), [
                'document_type_id' => 'required',
                'priority' => 'required',
                'request_message' => 'required'
            ]);

            if ($validator->fails()) {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Validation failed', 'errors' => $validator->errors()]);
                exit;
            }

            // Connect to database
            $pdo = new \PDO('mysql:host=127.0.0.1;dbname=shipment', 'root', 'root');
            $pdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // Get current user ID (assuming admin user ID is 1 for now)
            $adminUserId = 1;

            // Create document request
            $stmt = $pdo->prepare('
                INSERT INTO document_requests
                (shipment_id, document_type_id, requested_by, request_message, priority, due_date, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ');

            $dueDate = $this->input('due_date') ? date('Y-m-d H:i:s', strtotime($this->input('due_date'))) : null;

            $stmt->execute([
                $id,
                $this->input('document_type_id'),
                $adminUserId,
                $this->input('request_message'),
                $this->input('priority'),
                $dueDate,
                'pending'
            ]);

            $requestId = $pdo->lastInsertId();

            // Create notification if requested
            if ($this->input('send_notification') && !empty($shipment['receiver_email'])) {
                // Get document type name
                $stmt = $pdo->prepare('SELECT name FROM document_types WHERE id = ?');
                $stmt->execute([$this->input('document_type_id')]);
                $docType = $stmt->fetch(\PDO::FETCH_ASSOC);

                $stmt = $pdo->prepare('
                    INSERT INTO notifications
                    (shipment_id, document_request_id, type, title, message, recipient_email)
                    VALUES (?, ?, ?, ?, ?, ?)
                ');

                $stmt->execute([
                    $id,
                    $requestId,
                    'document_requested',
                    'Document Required for Shipment ' . $shipment['tracking_number'],
                    'A ' . $docType['name'] . ' is required for your shipment. Please upload the document as soon as possible.',
                    $shipment['receiver_email']
                ]);

                // Send email notification
                $this->sendDocumentRequestNotification($shipment, $docType['name'], $this->input('request_message'));
            }

            header('Content-Type: application/json');
            echo json_encode(['success' => true, 'message' => 'Document request sent successfully']);
            exit;

        } catch (\Exception $e) {
            error_log('Document Request Error: ' . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Error sending document request: ' . $e->getMessage()]);
            exit;
        }
    }

    /**
     * Send document request notification email
     */
    private function sendDocumentRequestNotification(array $shipment, string $documentType, string $message): bool
    {
        try {
            if (empty($shipment['receiver_email'])) {
                return false;
            }

            $subject = 'Document Required for Shipment ' . $shipment['tracking_number'];

            $body = "<html><body>";
            $body .= "<h2>Document Request</h2>";
            $body .= "<p>Hello " . htmlspecialchars($shipment['receiver_name']) . ",</p>";
            $body .= "<p>A document is required for your shipment with tracking number <strong>" . htmlspecialchars($shipment['tracking_number']) . "</strong>.</p>";
            $body .= "<p><strong>Document Type:</strong> " . htmlspecialchars($documentType) . "</p>";
            $body .= "<p><strong>Message:</strong> " . nl2br(htmlspecialchars($message)) . "</p>";

            // Add tracking link
            $trackingUrl = 'http://' . $_SERVER['HTTP_HOST'] . '/track?tracking_number=' . urlencode($shipment['tracking_number']);
            $body .= "<p>Please visit the tracking page to upload the required document: <a href='" . $trackingUrl . "'>" . $trackingUrl . "</a></p>";

            $body .= "<p>Thank you for your cooperation.</p>";
            $body .= "<p>Best regards,<br>The Shipping Team</p>";
            $body .= "</body></html>";

            $headers = "MIME-Version: 1.0\r\n";
            $headers .= "Content-type: text/html; charset=UTF-8\r\n";
            $headers .= "From: no-reply@" . $_SERVER['HTTP_HOST'] . "\r\n";

            return mail($shipment['receiver_email'], $subject, $body, $headers);

        } catch (\Exception $e) {
            error_log('Error sending document request notification: ' . $e->getMessage());
            return false;
        }
    }
}
