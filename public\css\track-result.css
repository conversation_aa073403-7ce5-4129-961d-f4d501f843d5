/* Track Result Page Styles */
:root {
    --primary-color: #333333;
    --secondary-color: #555555;
    --light-gray: #f8f9fa;
    --border-color: #e2e8f0;
    --text-color: #333333;
    --text-muted: #718096;
    --success-color: #4CAF50;
    --info-color: #2196F3;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --processing-color: #FF9800;
    --transit-color: #2196F3;
    --delivery-color: #9C27B0;
    --customs-color: #795548;
    --delay-color: #F44336;
    --arrival-color: #607D8B;
    --departure-color: #3F51B5;
    --sidebar-width: 60px;
    --header-height: 60px;
}

/* Layout */
.tracking-page-body {
    background-color: #f8f9fa;
    font-family: 'Inter', sans-serif;
    color: var(--text-color);
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

.tracking-container {
    max-width: 100%;
    width: 100%;
    height: 100vh;
    padding: 0;
    margin: 0;
    background-color: #ffffff;
    box-shadow: none;
    border-radius: 0;
    overflow: hidden;
}

.tracking-layout {
    display: flex;
    height: 100%;
}

/* Sidebar */
.tracking-sidebar {
    width: var(--sidebar-width);
    background-color: #ffffff;
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 0;
}

.sidebar-logo {
    margin-bottom: 30px;
    font-size: 24px;
}

.sidebar-logo a {
    color: var(--primary-color);
    text-decoration: none;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.nav-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: var(--text-muted);
    text-decoration: none;
    transition: all 0.2s;
}

.nav-item:hover {
    background-color: var(--light-gray);
    color: var(--primary-color);
}

.nav-item.active {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* Main Content */
.tracking-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.tracking-header {
    margin-bottom: 20px;
}

.tracking-title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 5px 0;
}

.tracking-subtitle {
    font-size: 14px;
    color: var(--text-muted);
    margin: 0 0 20px 0;
}

.tracking-search {
    margin-bottom: 20px;
}

.search-input-group {
    position: relative;
    max-width: 500px;
    display: flex;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary-color);
    z-index: 2;
}

.search-input {
    width: 100%;
    padding: 14px 15px 14px 40px;
    border: none;
    font-size: 14px;
    flex-grow: 1;
}

.search-input:focus {
    outline: none;
}

.search-button {
    padding: 0 20px;
    background-color: var(--primary-color);
    color: #ffffff;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    height: 100%;
    transition: background-color 0.2s;
}

.search-button:hover {
    background-color: var(--secondary-color);
}

/* Tracking Header Section */
.tracking-header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.tracking-header-left {
    display: flex;
    flex-direction: column;
}

.tracking-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: var(--text-color);
}

.tracking-number {
    font-size: 14px;
}

.tracking-number .label {
    font-weight: 500;
    color: var(--text-muted);
    margin-right: 5px;
}

.tracking-number .value {
    font-weight: 600;
    color: var(--text-color);
}

.tracking-barcode {
    margin-top: 10px;
    text-align: center;
}

.tracking-header-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.tracking-date {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 5px;
}

.order-status {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    background-color: var(--light-gray);
    display: flex;
    align-items: center;
    gap: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.status-icon {
    font-size: 8px;
}

.status-details {
    margin-top: 8px;
    text-align: right;
}

.status-message {
    font-size: 13px;
    color: var(--text-color);
    font-style: italic;
}

.order-status.delivered,
.order-status.completed {
    background-color: #F0FFF4;
    color: #2F855A;
}

.order-status.in-transit,
.order-status.processing {
    background-color: #EBF8FF;
    color: #2B6CB0;
}

.order-status.pending {
    background-color: #FFFAF0;
    color: #C05621;
}

.order-status.delayed,
.order-status.failed,
.order-status.cancelled {
    background-color: #FFF5F5;
    color: #C53030;
}

/* Progress Summary Section */
.progress-summary-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.progress-card {
    display: flex;
    align-items: center;
    padding: 10px;
    flex: 1;
}

.progress-icon {
    width: 40px;
    height: 40px;
    background-color: var(--light-gray);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 18px;
}

.progress-info {
    display: flex;
    flex-direction: column;
}

.progress-label {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.progress-value {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.progress-arrow {
    font-size: 20px;
    color: var(--text-muted);
    margin: 0 10px;
}

/* Shipment Info Grid */
.shipment-info-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.info-section {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.item-section {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    margin-bottom: 20px;
}

.section-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

.section-icon {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 16px;
    width: 20px;
    text-align: center;
    display: inline-block;
}

.section-title {
    flex: 1;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    color: var(--text-color);
    display: inline-block;
    line-height: 1;
}

.section-content {
    padding: 15px;
}

.info-row {
    display: flex;
    margin-bottom: 0;
    padding: 12px 0;
    border-bottom: none;
}

.section-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0;
    width: 100%;
}

/* Package Divider */
.package-divider {
    height: 2px;
    background-color: var(--border-color);
    margin: 20px 0;
}

/* Package Number */
.package-number {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px dashed var(--border-color);
}

/* Package Container */
.package-container, .item-container {
    margin-bottom: 15px;
}

/* Full-width info cards container */
.info-cards-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

/* Horizontal info card */
.info-card-horizontal {
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    width: 100%;
}

/* Card header */
.info-card-header {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: var(--light-gray);
    border-bottom: 1px solid var(--border-color);
}

/* Card content */
.info-card-content {
    padding: 15px;
}

/* Grid layout for details */
.info-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

/* Individual detail item */
.info-detail-item {
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
}

.detail-label {
    font-size: 12px;
    color: var(--text-muted);
    font-weight: 600;
    margin-bottom: 5px;
}

.detail-value {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    padding: 5px 8px;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

/* For package info layout */
.package-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.package-info-section {
    margin-bottom: 15px;
}

.package-separator {
    grid-column: 1 / -1;
    font-weight: 600;
    color: #4C51BF;
    background-color: #ffffff;
    border-radius: 4px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    padding: 8px 10px;
    margin: 10px 0 5px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .info-details-grid, .package-info-grid {
        grid-template-columns: 1fr;
    }
}

.info-label {
    width: 40%;
    font-size: 14px;
    color: var(--text-muted);
    font-weight: 500;
}

.info-value {
    width: 60%;
    font-size: 14px;
    color: var(--text-color);
    font-weight: 500;
    line-height: 1.5;
}

/* Tracking Search Container */
.tracking-search-container {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.tracking-form {
    max-width: 600px;
    margin: 0 auto;
}

.search-button {
    background-color: var(--primary-color);
    color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    margin-left: 10px;
}

.tracking-note {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 10px;
    display: flex;
    align-items: center;
}

.note-icon {
    margin-right: 5px;
}

.tracking-results-area {
    margin-top: 20px;
}

.tracking-error {
    background-color: #FFF5F5;
    color: #C53030;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.tracking-link {
    text-align: center;
    margin-top: 20px;
}

.view-details-link {
    display: inline-block;
    padding: 10px 20px;
    background-color: var(--primary-color);
    color: #ffffff;
    text-decoration: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
}

.view-details-link i {
    margin-right: 5px;
}

.result-container {
    margin-top: 20px;
    background-color: #f0f2f5;
    border-radius: 8px;
    padding: 20px;
}

/* Enhanced Tracking Details with Color Coding */
.tracking-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 992px) {
    .tracking-details {
        grid-template-columns: 1fr 1fr;
    }
}

/* Color-coded information sections */
.info-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.info-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.section-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 18px;
    color: white;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

/* Shipper Information - Blue theme */
.info-section:nth-child(1) .section-icon {
    background: linear-gradient(135deg, var(--info-color), #1976D2);
}

.info-section:nth-child(1) .section-header {
    border-bottom-color: var(--info-color);
}

/* Receiver Information - Green theme */
.info-section:nth-child(2) .section-icon {
    background: linear-gradient(135deg, var(--success-color), #388E3C);
}

.info-section:nth-child(2) .section-header {
    border-bottom-color: var(--success-color);
}

/* Shipment Details - Orange theme */
.info-section:nth-child(3) .section-icon {
    background: linear-gradient(135deg, var(--warning-color), #F57C00);
}

.info-section:nth-child(3) .section-header {
    border-bottom-color: var(--warning-color);
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
    padding: 8px 0;
}

.info-label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 80px;
}

.info-value {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
    text-align: right;
    flex: 1;
    margin-left: 15px;
}

.section-divider {
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, var(--border-color) 50%, transparent 100%);
    margin: 8px 0;
}

/* Enhanced Horizontal Information Cards */
.info-card-horizontal {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: all 0.3s ease;
}

.info-card-horizontal:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.info-card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 20px 25px;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    align-items: center;
}

.info-card-header .section-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.info-card-header .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.info-card-content {
    padding: 25px;
}

.info-details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.info-detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 120px;
}

.detail-value {
    font-weight: 500;
    color: var(--text-color);
    font-size: 14px;
    text-align: right;
    flex: 1;
    margin-left: 15px;
}

/* Color themes for different card types */
.info-card-horizontal:nth-of-type(1) .info-card-header .section-icon {
    background: linear-gradient(135deg, #FF9800, #F57C00); /* Orange for shipping */
}

.info-card-horizontal:nth-of-type(1) .info-card-header {
    border-bottom-color: #FF9800;
}

.info-card-horizontal:nth-of-type(2) .info-card-header .section-icon {
    background: linear-gradient(135deg, #2196F3, #1976D2); /* Blue for dates */
}

.info-card-horizontal:nth-of-type(2) .info-card-header {
    border-bottom-color: #2196F3;
}

.info-card-horizontal:nth-of-type(3) .info-card-header .section-icon {
    background: linear-gradient(135deg, #4CAF50, #388E3C); /* Green for packages */
}

.info-card-horizontal:nth-of-type(3) .info-card-header {
    border-bottom-color: #4CAF50;
}

.info-card-horizontal:nth-of-type(4) .info-card-header .section-icon {
    background: linear-gradient(135deg, #9C27B0, #7B1FA2); /* Purple for items */
}

.info-card-horizontal:nth-of-type(4) .info-card-header {
    border-bottom-color: #9C27B0;
}

/* Enhanced Progress Summary Section */
.progress-summary-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    margin: 25px 0;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.progress-card {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    padding: 15px;
    transition: all 0.3s ease;
}

.progress-card:hover {
    transform: translateY(-3px);
}

.progress-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 24px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.progress-card:nth-child(1) .progress-icon {
    background: linear-gradient(135deg, var(--success-color), #388E3C);
}

.progress-card:nth-child(3) .progress-icon {
    background: linear-gradient(135deg, var(--info-color), #1976D2);
}

.progress-card:nth-child(5) .progress-icon {
    background: linear-gradient(135deg, var(--warning-color), #F57C00);
}

.progress-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.progress-label {
    font-weight: 600;
    color: var(--text-muted);
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 8px;
}

.progress-value {
    font-weight: 600;
    color: var(--text-color);
    font-size: 16px;
}

.progress-arrow {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    font-size: 24px;
    margin: 0 15px;
    animation: arrowPulse 2s infinite;
}

@keyframes arrowPulse {
    0%, 100% { opacity: 0.6; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.1); }
}

/* Map Section */
.map-section, .chart-section, .route-section, .timeline-section {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.map-container {
    height: 400px; /* Increased height for better visibility */
    width: 100%;
}

#shipment-map {
    width: 100%;
    height: 100%;
}

/* Custom map markers */
.marker-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    border: 2px solid #fff;
    color: #4C51BF;
    font-size: 18px;
}

.origin-icon {
    background-color: #EBF4FF;
    color: #4C51BF;
    border-color: #4C51BF;
}

.destination-icon {
    background-color: #F0FFF4;
    color: #38A169;
    border-color: #38A169;
}

.truck-icon {
    background-color: #4C51BF;
    color: white;
    border-color: #4C51BF;
    font-size: 22px;
    animation: pulse 1.5s infinite;
}

/* Current location marker animation */
.current-location-marker {
    z-index: 1000 !important;
}

/* Chart Section */
#shipment-status-chart {
    padding: 20px;
    min-height: 250px;
}

/* Top Route Section */
.top-route-section {
    margin-top: 0;
    margin-bottom: 20px;
    padding: 20px 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
    box-shadow: none;
}

/* After Tracking Details Route Section */
.after-tracking-details {
    margin-top: 0;
    margin-bottom: 20px;
    padding: 20px 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
    box-shadow: none;
}

/* Delivery Route Timeline */
.delivery-route-timeline {
    padding: 20px;
}

.delivery-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin: 20px 0;
    padding: 0 20px;
}

.delivery-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    flex: 1;
    z-index: 2;
}

.step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    color: #a0aec0;
    font-size: 20px;
    transition: all 0.3s ease;
}

.step-connector {
    position: absolute;
    top: 25px;
    left: calc(50% + 25px);
    right: calc(-50% + 25px);
    height: 2px;
    background-color: #e2e8f0;
    z-index: 1;
}

.delivery-step:last-child .step-connector {
    display: none;
}

.step-content {
    text-align: center;
    max-width: 120px;
}

.step-label {
    font-size: 14px;
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 5px;
}

.step-description {
    font-size: 12px;
    color: #718096;
    margin-bottom: 5px;
}

.step-status {
    font-size: 11px;
    font-weight: 600;
    color: white;
    background-color: #4c51bf;
    padding: 3px 8px;
    border-radius: 10px;
    display: inline-block;
    margin-top: 4px;
    margin-bottom: 4px;
}

/* Step date and location */
.step-date {
    font-size: 11px;
    color: #4a5568;
    margin-top: 4px;
    font-weight: 500;
}

.step-location {
    font-size: 11px;
    color: #718096;
    margin-top: 2px;
}

.step-location i {
    font-size: 10px;
    margin-right: 3px;
}

/* Completed steps */
.delivery-step.completed .step-icon {
    background-color: #4c51bf;
    border-color: #4c51bf;
    color: #ffffff;
}

.delivery-step.completed .step-connector {
    background-color: #4c51bf;
}

/* Current step */
.delivery-step.current .step-icon {
    background-color: #ffffff;
    border-color: #4c51bf;
    border-width: 3px;
    color: #4c51bf;
    box-shadow: 0 0 0 5px rgba(76, 81, 191, 0.1);
    animation: currentPulse 1.5s infinite;
}

.delivery-step.current .step-label {
    color: #4c51bf;
    font-weight: 700;
}

/* Enhanced Color-Coded Timeline Styles */
.delivery-step.custom {
    border-top: 2px solid var(--warning-color);
    padding-top: 10px;
    margin-top: 10px;
}

.delivery-step.custom .step-label {
    font-weight: 700;
}

.step-message {
    font-size: 10px;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 5px;
    line-height: 1.2;
}

/* Enhanced animations */
@keyframes currentPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 5px rgba(76, 81, 191, 0.1);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(76, 81, 191, 0.2);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 5px rgba(76, 81, 191, 0.1);
    }
}

.delivery-step.completed .step-icon {
    animation: completedPulse 2s infinite;
}

@keyframes completedPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

/* Simple Vertical Tracking Details Styles */
.tracking-details-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.tracking-details-section .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--info-color);
}

.tracking-details-section .section-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--info-color), #1976D2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tracking-details-section .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.tracking-details-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tracking-detail-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    position: relative;
}

.tracking-detail-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.tracking-detail-item.current-status {
    background: linear-gradient(135deg, #ffffff 0%, #f8fff8 100%);
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.15);
}

.detail-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    flex-shrink: 0;
}

.detail-content {
    flex: 1;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
    flex-wrap: wrap;
    gap: 10px;
}

.detail-status {
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.current-badge {
    background: var(--success-color);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-date {
    font-size: 14px;
    color: var(--text-muted);
    font-weight: 500;
}

.detail-location {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.detail-location i {
    color: var(--info-color);
}

.detail-message {
    font-size: 14px;
    color: var(--text-color);
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    margin-top: 8px;
    border-left: 3px solid var(--info-color);
}

.detail-remarks {
    font-size: 13px;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 5px;
}

.no-tracking-details {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
    font-size: 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed var(--border-color);
}

/* Document Management Styles */
.document-management-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
}

.document-management-section .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--info-color);
}

.document-management-section .section-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--info-color), #1976D2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.document-management-section .section-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
}

.document-requests-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.document-request-item {
    background: #ffffff;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
}

.document-request-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.document-request-item.overdue {
    border-left: 4px solid #F44336;
    background: linear-gradient(135deg, #ffffff 0%, #fff5f5 100%);
}

.document-request-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    flex-wrap: wrap;
    gap: 10px;
}

.document-type {
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-type i {
    color: var(--info-color);
    font-size: 18px;
}

.type-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
}

.priority-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.priority-low { background: #E8F5E8; color: #2E7D32; }
.priority-medium { background: #FFF3E0; color: #F57C00; }
.priority-high { background: #FFEBEE; color: #D32F2F; }
.priority-urgent { background: #F3E5F5; color: #7B1FA2; }

.status-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-pending { background: #FFF3E0; color: #F57C00; }
.status-uploaded { background: #E3F2FD; color: #1976D2; }
.status-approved { background: #E8F5E8; color: #2E7D32; }
.status-rejected { background: #FFEBEE; color: #D32F2F; }
.status-cancelled { background: #F5F5F5; color: #757575; }

.document-request-content {
    margin-top: 15px;
}

.request-message {
    background: #f8f9fa;
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 15px;
    border-left: 3px solid var(--info-color);
    font-size: 14px;
    color: var(--text-color);
}

.due-date {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: var(--text-muted);
    margin-bottom: 15px;
}

.due-date.overdue {
    color: #D32F2F;
    font-weight: 600;
}

.due-date i {
    color: var(--info-color);
}

.overdue-label {
    background: #D32F2F;
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: 600;
    margin-left: 8px;
}

.document-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.upload-document-btn, .reupload-document-btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.upload-document-btn {
    background: var(--primary-color);
    color: white;
}

.upload-document-btn:hover {
    background: #1976D2;
    transform: translateY(-1px);
}

.reupload-document-btn {
    background: #6c757d;
    color: white;
}

.reupload-document-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.upload-status, .approval-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    font-weight: 500;
}

.approval-status.approved {
    color: #2E7D32;
}

.approval-status.rejected {
    color: #D32F2F;
}

.no-document-requests, .error-loading-documents {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.no-document-requests i, .error-loading-documents i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--info-color);
}

.no-document-requests h4, .error-loading-documents h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
}

.loading-documents {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.loading-documents i {
    font-size: 24px;
    margin-right: 10px;
    color: var(--info-color);
}

/* Document Upload Modal Styles */
.document-upload-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 18px;
    font-weight: 600;
}

.close-modal {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background: #f5f5f5;
    color: var(--text-color);
}

.modal-body {
    padding: 25px;
}

.file-upload-area {
    position: relative;
    margin-bottom: 20px;
}

.file-upload-area input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-area label {
    display: block;
    padding: 40px 20px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.file-upload-area label:hover {
    border-color: var(--primary-color);
    background: #f0f8ff;
}

.file-upload-area label i {
    font-size: 32px;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: block;
}

.file-upload-area label span {
    display: block;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 5px;
}

.file-upload-area label small {
    display: block;
    font-size: 12px;
    color: var(--text-muted);
}

.upload-notes {
    margin-bottom: 20px;
}

.upload-notes label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.upload-notes textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-family: inherit;
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
}

.upload-notes textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.1);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px 25px;
    border-top: 1px solid var(--border-color);
    background: #f8f9fa;
    border-radius: 0 0 12px 12px;
}

.modal-footer .btn {
    padding: 10px 20px;
    border-radius: 6px;
    border: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-footer .btn-secondary {
    background: #6c757d;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background: #5a6268;
}

.modal-footer .btn-primary {
    background: var(--primary-color);
    color: white;
}

.modal-footer .btn-primary:hover {
    background: #1976D2;
}

/* Functional Sidebar Styles */
.sidebar-sections {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 20px 0;
    padding: 0 10px;
}

.sidebar-section {
    position: relative;
}

.sidebar-toggle {
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sidebar-toggle:hover {
    background: linear-gradient(135deg, var(--primary-color), #1976D2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.sidebar-toggle.active {
    background: linear-gradient(135deg, var(--primary-color), #1976D2);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: #F44336;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 11px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.sidebar-panel {
    position: fixed;
    top: 0;
    left: 80px;
    width: 350px;
    height: 100vh;
    background: white;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
    transform: translateX(-430px);
    transition: transform 0.3s ease;
    z-index: 2000;
    display: none;
    flex-direction: column;
    border-radius: 0 12px 12px 0;
}

.sidebar-panel.active {
    display: flex;
    transform: translateX(0);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.panel-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.panel-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.panel-close:hover {
    background: #f5f5f5;
    color: var(--text-color);
}

.panel-content {
    flex: 1;
    padding: 20px 25px;
    overflow-y: auto;
}

.no-content {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-muted);
}

.no-content i {
    font-size: 48px;
    margin-bottom: 15px;
    color: var(--info-color);
    opacity: 0.5;
}

.no-content p {
    margin: 0;
    font-size: 14px;
}

/* Document History Styles */
.document-history-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--info-color);
    transition: all 0.3s ease;
}

.document-history-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.document-history-item.approved {
    border-left-color: #4CAF50;
}

.document-history-item.rejected {
    border-left-color: #F44336;
}

.document-history-item.pending {
    border-left-color: #FF9800;
}

.document-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.document-history-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.document-history-status {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

.document-history-date {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.document-history-notes {
    font-size: 12px;
    color: var(--text-color);
    font-style: italic;
}

/* Notification Styles */
.notification-item {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid var(--info-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.notification-item:hover {
    background: #e9ecef;
    transform: translateY(-1px);
}

.notification-item.unread {
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    border-left-color: var(--primary-color);
}

.notification-item.document_requested {
    border-left-color: #FF9800;
}

.notification-item.document_approved {
    border-left-color: #4CAF50;
}

.notification-item.document_rejected {
    border-left-color: #F44336;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notification-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.notification-time {
    font-size: 11px;
    color: var(--text-muted);
}

.notification-message {
    font-size: 12px;
    color: var(--text-color);
    line-height: 1.4;
}

.notification-unread-indicator {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    margin-left: 8px;
}

/* Track Details Condensed Styles */
.condensed-track-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.condensed-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.condensed-status-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
}

.condensed-status-text {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.condensed-details {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.4;
}

.condensed-details div {
    margin-bottom: 5px;
}

.condensed-details strong {
    color: var(--text-color);
}

/* Tabs */
.tabs-container {
    background-color: #ffffff;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
}

.tabs-header {
    display: flex;
    overflow-x: auto;
    border-bottom: 1px solid var(--border-color);
}

.tab-btn {
    padding: 15px;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-muted);
    white-space: nowrap;
}

.tab-btn.active {
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.tab-content {
    padding: 20px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Timeline */
.shipment-timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-event {
    position: relative;
    margin-bottom: 25px;
    padding-bottom: 5px;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.timeline-event.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.timeline-event:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
}

.event-dot {
    position: absolute;
    left: -30px;
    top: 0;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #FFFFFF;
    border: 2px solid #CBD5E0;
    z-index: 1;
    transition: all 0.3s ease;
}

.event-dot.active {
    border-color: var(--primary-color);
    background-color: var(--primary-color);
}

.event-dot.animate-pulse {
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 81, 191, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(76, 81, 191, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 81, 191, 0);
    }
}

.event-content {
    padding-left: 10px;
    background-color: #f8fafc;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid #e2e8f0;
}

.event-date {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 5px;
}

.event-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 5px;
}

.event-location {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.event-message {
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
}

.event-remarks {
    font-size: 12px;
    color: var(--text-color);
    margin-top: 5px;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid var(--primary-color);
}

.no-events {
    color: var(--text-muted);
    font-style: italic;
    margin-top: 15px;
}

/* Placeholder Content */
.placeholder-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: var(--text-muted);
    text-align: center;
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 20px;
    opacity: 0.5;
}

/* Status Chart Styles */
.shipment-status-section {
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.status-chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    height: 200px;
    margin-top: 20px;
}

.chart-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 5px;
}

.chart-bar-container {
    width: 100%;
    height: 180px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
}

.chart-bar {
    width: 30px;
    background-color: #4c51bf;
    border-radius: 4px 4px 0 0;
    position: relative;
    opacity: 0;
    transform: scaleY(0);
    transform-origin: bottom;
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.bar-value {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: #4a5568;
    font-weight: 600;
}

.chart-label {
    margin-top: 10px;
    font-size: 0.75rem;
    color: #718096;
}

/* Delivery Route Map */
.delivery-map-section {
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
}

.route-map-placeholder {
    height: 150px;
    background-color: #f8fafc;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px;
    margin-top: 20px;
}

.map-point {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    z-index: 2;
}

.map-point i {
    font-size: 1.5rem;
}

.map-point span {
    font-size: 0.75rem;
    color: #4a5568;
}

.map-point.origin i {
    color: #38a169;
}

.map-point.current i {
    color: #4c51bf;
}

.map-point.destination i {
    color: #e53e3e;
}

.map-route-line {
    flex-grow: 1;
    height: 3px;
    background: linear-gradient(90deg, #38a169, #4c51bf, #e53e3e);
    position: relative;
}

.map-route-line::before {
    content: '';
    position: absolute;
    top: -3px;
    left: 0;
    width: 70%;
    height: 9px;
    background-color: rgba(76, 81, 191, 0.2);
    border-radius: 4px;
}

/* Driver Info */
.driver-info {
    padding: 20px;
}

.driver-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.driver-avatar {
    font-size: 48px;
    margin-right: 20px;
    color: var(--text-muted);
}

.driver-name {
    font-size: 18px;
    font-weight: 600;
}

.driver-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.driver-detail {
    margin-bottom: 10px;
}

.driver-detail .label {
    font-size: 12px;
    color: var(--text-muted);
    display: block;
    margin-bottom: 5px;
}

.driver-detail .value {
    font-size: 14px;
    font-weight: 500;
}

/* Item List */
.item-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.item-list-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.item-list-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    width: 36px;
    height: 36px;
    border-radius: 4px;
    background-color: var(--light-gray);
    border: none;
    color: var(--text-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item-list-table {
    width: 100%;
    overflow-x: auto;
}

.item-list-table table {
    width: 100%;
    border-collapse: collapse;
}

.item-list-table th,
.item-list-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.item-list-table th {
    font-weight: 600;
    background-color: var(--light-gray);
    color: var(--text-color);
}

.item-list-table tr:last-child td {
    border-bottom: none;
}

.item-action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-muted);
}

/* Customer Info */
.customer-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
}

@media (min-width: 768px) {
    .customer-info {
        grid-template-columns: repeat(2, 1fr);
    }
}

.customer-section {
    margin-bottom: 20px;
}

.customer-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

.customer-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
}

.customer-detail {
    margin-bottom: 10px;
}

.customer-detail .label {
    font-size: 12px;
    color: var(--text-muted);
    display: block;
    margin-bottom: 5px;
}

.customer-detail .value {
    font-size: 14px;
    font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .tracking-layout {
        flex-direction: column;
    }

    .tracking-sidebar {
        width: 100%;
        height: var(--header-height);
        flex-direction: row;
        padding: 0 20px;
        border-right: none;
        border-bottom: 1px solid var(--border-color);
    }

    .sidebar-logo {
        margin-bottom: 0;
        margin-right: 20px;
    }

    .sidebar-nav {
        flex-direction: row;
    }

    .tracking-content {
        height: calc(100vh - var(--header-height));
        overflow-y: auto;
    }

    .tracking-header-section {
        flex-direction: column;
        align-items: flex-start;
    }

    .tracking-header-right {
        align-items: flex-start;
        margin-top: 15px;
    }

    .progress-summary-section {
        flex-direction: column;
        padding: 15px;
    }

    .progress-card {
        width: 100%;
        margin-bottom: 10px;
    }

    .progress-arrow {
        transform: rotate(90deg);
        margin: 10px 0;
    }

    .shipment-info-grid {
        grid-template-columns: 1fr;
    }

    .info-section {
        margin-bottom: 15px;
    }

    .info-row {
        flex-direction: column;
    }

    .info-label, .info-value {
        width: 100%;
    }

    .info-label {
        margin-bottom: 5px;
    }

    .map-container {
        height: 250px;
    }

    .status-chart {
        overflow-x: auto;
        padding-bottom: 15px;
    }

    .chart-column {
        min-width: 60px;
    }

    .route-map-placeholder {
        padding: 0 15px;
    }

    .delivery-steps {
        flex-direction: column;
        padding: 0;
        margin-left: 20px;
    }

    .delivery-step {
        flex-direction: row;
        align-items: flex-start;
        margin-bottom: 30px;
    }

    .delivery-step:last-child {
        margin-bottom: 0;
    }

    .step-icon {
        margin-right: 15px;
        margin-bottom: 0;
    }

    .step-content {
        text-align: left;
        max-width: none;
    }

    .step-connector {
        top: 50px;
        left: 25px;
        width: 2px;
        height: 30px;
        right: auto;
    }

    /* Enhanced responsive design for color-coded sections */
    .info-details-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .info-card-header {
        padding: 15px 20px;
    }

    .info-card-content {
        padding: 20px;
    }

    .section-icon {
        width: 35px;
        height: 35px;
        font-size: 16px;
    }

    .info-card-header .section-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
    }

    .detail-label {
        min-width: auto;
        margin-bottom: 5px;
    }

    .info-detail-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .detail-value {
        text-align: left;
        margin-left: 0;
    }

    /* Responsive styles for vertical tracking details */
    .tracking-details-section {
        padding: 20px;
        margin: 15px 0;
    }

    .tracking-details-section .section-header {
        margin-bottom: 20px;
        padding-bottom: 12px;
    }

    .tracking-details-section .section-icon {
        width: 40px;
        height: 40px;
        font-size: 18px;
        margin-right: 12px;
    }

    .tracking-details-section .section-title {
        font-size: 18px;
    }

    .tracking-detail-item {
        padding: 15px;
        flex-direction: column;
        align-items: flex-start;
    }

    .detail-icon {
        width: 45px;
        height: 45px;
        font-size: 18px;
        margin-right: 0;
        margin-bottom: 15px;
        align-self: center;
    }

    .detail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
        margin-bottom: 12px;
    }

    .detail-status {
        font-size: 16px;
    }

    .detail-date {
        font-size: 13px;
    }

    .detail-location {
        font-size: 13px;
        margin-bottom: 6px;
    }

    .detail-message {
        font-size: 13px;
        padding: 8px;
        margin-top: 6px;
    }
}
