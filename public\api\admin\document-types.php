<?php
require_once __DIR__ . '/../../bootstrap.php';

use App\Core\Database;
use App\Core\Session;
use App\Core\View;

// Set JSON header
header('Content-Type: application/json');

// Check if user is authenticated as admin
if (!Session::has('admin_user_id')) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGet();
            break;
        case 'POST':
            handlePost();
            break;
        case 'PUT':
            handlePut();
            break;
        case 'DELETE':
            handleDelete();
            break;
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'message' => 'Method not allowed']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Server error: ' . $e->getMessage()]);
}

function handleGet() {
    if (isset($_GET['id'])) {
        // Get single document type
        $id = (int)$_GET['id'];
        Database::prepare("SELECT * FROM document_types WHERE id = :id");
        Database::bindValue(':id', $id, PDO::PARAM_INT);
        Database::execute();
        $documentType = Database::fetch();
        
        if ($documentType) {
            echo json_encode(['success' => true, 'data' => $documentType]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'message' => 'Document type not found']);
        }
    } else {
        // Get all document types
        Database::prepare("SELECT * FROM document_types ORDER BY name ASC");
        Database::execute();
        $documentTypes = Database::fetchAll();
        
        echo json_encode(['success' => true, 'data' => $documentTypes]);
    }
}

function handlePost() {
    // Verify CSRF token
    if (!View::verifyCsrf($_POST['_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        return;
    }
    
    // Validate required fields
    if (empty($_POST['name'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Document type name is required']);
        return;
    }
    
    $name = trim($_POST['name']);
    $description = trim($_POST['description'] ?? '');
    $isActive = (int)($_POST['is_active'] ?? 1);

    // Check if document type already exists
    Database::prepare("SELECT id FROM document_types WHERE name = :name");
    Database::bindValue(':name', $name, PDO::PARAM_STR);
    Database::execute();

    if (Database::fetch()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Document type with this name already exists']);
        return;
    }

    // Insert new document type (using existing table structure)
    Database::prepare("
        INSERT INTO document_types (name, description, is_predefined, is_active, created_at, updated_at)
        VALUES (:name, :description, 0, :is_active, NOW(), NOW())
    ");

    Database::bindValue(':name', $name, PDO::PARAM_STR);
    Database::bindValue(':description', $description, PDO::PARAM_STR);
    Database::bindValue(':is_active', $isActive, PDO::PARAM_INT);
    
    if (Database::execute()) {
        $newId = Database::lastInsertId();
        echo json_encode(['success' => true, 'message' => 'Document type created successfully', 'id' => $newId]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to create document type']);
    }
}

function handlePut() {
    // Parse PUT data
    parse_str(file_get_contents("php://input"), $putData);
    
    // Verify CSRF token
    if (!View::verifyCsrf($putData['_token'] ?? '')) {
        http_response_code(403);
        echo json_encode(['success' => false, 'message' => 'Invalid CSRF token']);
        return;
    }
    
    if (empty($putData['id']) || empty($putData['name'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID and name are required']);
        return;
    }
    
    $id = (int)$putData['id'];
    $name = trim($putData['name']);
    $description = trim($putData['description'] ?? '');
    $isActive = (int)($putData['is_active'] ?? 1);
    
    // Check if document type exists
    Database::prepare("SELECT id FROM document_types WHERE id = :id");
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    Database::execute();
    
    if (!Database::fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Document type not found']);
        return;
    }
    
    // Check if name is already taken by another document type
    Database::prepare("SELECT id FROM document_types WHERE name = :name AND id != :id");
    Database::bindValue(':name', $name, PDO::PARAM_STR);
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    Database::execute();
    
    if (Database::fetch()) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Document type with this name already exists']);
        return;
    }
    
    // Update document type
    Database::prepare("
        UPDATE document_types
        SET name = :name, description = :description, is_active = :is_active, updated_at = NOW()
        WHERE id = :id
    ");
    
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    Database::bindValue(':name', $name, PDO::PARAM_STR);
    Database::bindValue(':description', $description, PDO::PARAM_STR);
    Database::bindValue(':is_active', $isActive, PDO::PARAM_INT);
    
    if (Database::execute()) {
        echo json_encode(['success' => true, 'message' => 'Document type updated successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to update document type']);
    }
}

function handleDelete() {
    if (empty($_GET['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'ID is required']);
        return;
    }
    
    $id = (int)$_GET['id'];
    
    // Check if document type exists
    Database::prepare("SELECT id FROM document_types WHERE id = :id");
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    Database::execute();
    
    if (!Database::fetch()) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Document type not found']);
        return;
    }
    
    // Check if document type is being used in any document requests
    Database::prepare("SELECT COUNT(*) as count FROM document_requests WHERE document_type_id = :id");
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    Database::execute();
    $result = Database::fetch();
    
    if ($result['count'] > 0) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Cannot delete document type that is being used in document requests']);
        return;
    }
    
    // Delete document type
    Database::prepare("DELETE FROM document_types WHERE id = :id");
    Database::bindValue(':id', $id, PDO::PARAM_INT);
    
    if (Database::execute()) {
        echo json_encode(['success' => true, 'message' => 'Document type deleted successfully']);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'message' => 'Failed to delete document type']);
    }
}
?>
